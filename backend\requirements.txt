# Core FastAPI and web framework
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Database
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
redis>=5.0.0

# AI/ML Libraries
openai>=1.0.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Natural Language Processing
nltk>=3.8.0

# HTTP and API clients
httpx>=0.25.0
requests>=2.31.0

# Data validation and serialization
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Logging and monitoring
loguru>=0.7.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Environment and configuration
python-dotenv>=1.0.0

# Date and time handling
python-dateutil>=2.8.0

# File handling and utilities
aiofiles>=23.0.0

# Security
cryptography>=40.0.0
bcrypt>=4.0.0