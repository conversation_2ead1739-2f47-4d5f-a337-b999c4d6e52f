<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Education Agent - Government Schools</title>
    <meta name="description" content="Intelligent tutoring system for government schools with personalized learning, adaptive worksheets, and AI-powered doubt clearing.">
    <meta name="keywords" content="AI education, government schools, personalized learning, tutoring system">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="AI Education Agent - Government Schools">
    <meta property="og:description" content="Intelligent tutoring system with personalized learning and AI-powered doubt clearing">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://agent-minds.vercel.app">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="AI Education Agent - Government Schools">
    <meta name="twitter:description" content="Intelligent tutoring system with personalized learning and AI-powered doubt clearing">
    
    <!-- Redirect to demo.html immediately -->
    <script>
        // Immediate redirect to demo.html
        window.location.replace('./demo.html');
    </script>
    
    <!-- Fallback meta refresh in case JavaScript is disabled -->
    <meta http-equiv="refresh" content="0; url=./demo.html">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        .loading-container {
            max-width: 400px;
            padding: 2rem;
        }
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .fallback-link {
            color: white;
            text-decoration: underline;
            margin-top: 1rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">🤖</div>
        <h1>AI Education Agent</h1>
        <p>Loading the intelligent tutoring system...</p>
        <div class="spinner"></div>
        <p><small>If you're not redirected automatically, <a href="./demo.html" class="fallback-link">click here</a></small></p>
    </div>
</body>
</html>
