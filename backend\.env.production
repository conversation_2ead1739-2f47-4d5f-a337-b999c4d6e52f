# AI Education Agent - Production Environment Configuration

# OpenAI Configuration
OPENAI_API_KEY=AIzaSyDhQduSfCjBzsEAUNwjEXTfSXon2gqkUMs

# Production Database Configuration
DATABASE_URL=*********************************************************************/ai_education_prod
REDIS_URL=redis://your-redis-host:6379/0

# Security (Generate strong random keys)
SECRET_KEY=your-super-strong-secret-key-for-production-256-bits
JWT_SECRET_KEY=your-jwt-secret-key-for-production-256-bits

# Application Settings
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Frontend Configuration
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_APP_NAME=AI Education Agent

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Storage
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# AI Model Configuration
DEFAULT_MODEL=gpt-4
FALLBACK_MODEL=gpt-3.5-turbo
MAX_TOKENS=1000
TEMPERATURE=0.7

# Performance Settings
CACHE_TTL=3600
SESSION_TIMEOUT=86400
RATE_LIMIT_PER_MINUTE=100

# Security Headers
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
SECURE_CONTENT_TYPE_NOSNIFF=true
SECURE_BROWSER_XSS_FILTER=true

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking
ANALYTICS_ENDPOINT=https://your-analytics-endpoint.com

# Localization
DEFAULT_LANGUAGE=english
SUPPORTED_LANGUAGES=english,hindi,tamil,bengali,gujarati,marathi