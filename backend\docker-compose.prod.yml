version: '3.8'

services:
  # PostgreSQL Database - Production
  postgres:
    image: postgres:15-alpine
    container_name: ai_education_db_prod
    environment:
      POSTGRES_DB: ai_education_prod
      POSTGRES_USER: ai_education_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - ai_education_network
    restart: unless-stopped
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB

  # Redis Cache - Production
  redis:
    image: redis:7-alpine
    container_name: ai_education_redis_prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    networks:
      - ai_education_network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Backend API - Production
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: ai_education_backend_prod
    env_file:
      - .env.production
    environment:
      - DATABASE_URL=postgresql://ai_education_user:${DB_PASSWORD}@postgres:5432/ai_education_prod
      - REDIS_URL=redis://redis:6379
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - ai_education_network
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Application - Production
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: ai_education_frontend_prod
    environment:
      - NEXT_PUBLIC_API_URL=https://api.your-domain.com
      - NODE_ENV=production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - ai_education_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ai_education_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - ai_education_network
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data:

networks:
  ai_education_network:
    driver: bridge