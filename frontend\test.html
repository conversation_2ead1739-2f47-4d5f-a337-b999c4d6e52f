<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - AI Education Agent</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6">Testing AI Education Agent Features</h1>
        
        <!-- Test Icons -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Icon Test</h2>
            <div class="grid grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-2 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-lg">▶</span>
                    </div>
                    <p class="text-sm">Play Icon</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-2 bg-green-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">📝</span>
                    </div>
                    <p class="text-sm">Worksheet Icon</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-2 bg-purple-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-xs">🤖</span>
                    </div>
                    <p class="text-sm">AI Icon</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">📊</span>
                    </div>
                    <p class="text-sm">Chart Icon</p>
                </div>
            </div>
        </div>

        <!-- Test Chatbot -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">Chatbot Test</h2>
            <div class="space-y-4">
                <textarea id="test-question" placeholder="Type a test question here..." class="w-full border border-gray-300 rounded-lg px-3 py-2 h-20"></textarea>
                <button onclick="testChatbot()" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg">
                    Test Chatbot Response
                </button>
                <div id="test-response" class="bg-gray-50 rounded-lg p-4 min-h-20">
                    <p class="text-gray-500">Response will appear here...</p>
                </div>
            </div>
        </div>

        <!-- Navigation Test -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h2 class="text-lg font-semibold mb-4">Navigation Test</h2>
            <div class="space-x-4">
                <button onclick="window.location.href='/demo.html'" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg">
                    Go to Main Demo
                </button>
                <button onclick="testAskAI()" class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg">
                    Test Ask AI Function
                </button>
            </div>
        </div>
    </div>

    <script>
        function testChatbot() {
            const question = document.getElementById('test-question').value;
            const responseDiv = document.getElementById('test-response');
            
            if (!question.trim()) {
                responseDiv.innerHTML = '<p class="text-red-500">Please enter a question first!</p>';
                return;
            }

            responseDiv.innerHTML = '<p class="text-blue-500">Processing your question...</p>';
            
            setTimeout(() => {
                responseDiv.innerHTML = `
                    <div class="space-y-2">
                        <p class="font-medium">Question: "${question}"</p>
                        <p class="text-green-600">✅ Chatbot is working correctly!</p>
                        <p class="text-gray-600">This simulates the AI response functionality.</p>
                    </div>
                `;
            }, 1000);
        }

        function testAskAI() {
            alert('Testing askAI() function - this would normally switch to the AI Features tab');
            console.log('askAI() function test called');
        }
    </script>
</body>
</html>
