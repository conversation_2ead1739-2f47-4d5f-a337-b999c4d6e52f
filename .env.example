# AI Education Agent Environment Configuration
# Copy this file to .env and fill in your actual values

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
DATABASE_URL=postgresql://ai_user:ai_password@localhost:5432/ai_education
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your_secret_key_here_generate_a_strong_random_string
JWT_SECRET_KEY=your_jwt_secret_key_here

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=AI Education Agent

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# File Storage (for content and media)
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes

# AI Model Configuration
DEFAULT_MODEL=gpt-4
FALLBACK_MODEL=gpt-3.5-turbo
MAX_TOKENS=1000
TEMPERATURE=0.7

# Analytics and Monitoring
ENABLE_ANALYTICS=true
ANALYTICS_ENDPOINT=https://your-analytics-endpoint.com

# Government Integration (placeholder for future integrations)
GOV_API_ENDPOINT=https://api.education.gov.in
GOV_API_KEY=your_government_api_key

# Localization
DEFAULT_LANGUAGE=english
SUPPORTED_LANGUAGES=english,hindi,tamil,bengali

# Performance Settings
CACHE_TTL=3600  # 1 hour in seconds
SESSION_TIMEOUT=86400  # 24 hours in seconds
RATE_LIMIT_PER_MINUTE=60