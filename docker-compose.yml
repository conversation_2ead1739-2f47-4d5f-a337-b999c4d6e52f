version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ai_education_db
    environment:
      POSTGRES_DB: ai_education
      POSTGRES_USER: ai_user
      POSTGRES_PASSWORD: ai_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - ai_education_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ai_education_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai_education_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai_education_backend
    environment:
      - DATABASE_URL=**********************************************/ai_education
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - ai_education_network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai_education_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ai_education_network
    command: npm run dev

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: ai_education_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - ai_education_network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  ai_education_network:
    driver: bridge