{"name": "ai-education-agent-frontend", "version": "1.0.0", "description": "Frontend for AI Education Agent - Personalized learning system for government schools", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "axios": "^1.6.0", "react-query": "^3.39.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}